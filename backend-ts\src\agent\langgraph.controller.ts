import {
  <PERSON>,
  <PERSON>,
  HttpCode,
  Body,
  HttpException,
  HttpStatus,
  <PERSON>s,
  Req,
  Get,
  Param,
  UsePipes,
} from '@nestjs/common';
import { FastifyReply, FastifyRequest } from 'fastify';
import { GraphService } from './graph.service';
import { OverallState } from './state';
import { BaseMessage, HumanMessage, AIMessage, SystemMessage } from '@langchain/core/messages';
import { v4 as uuidv4 } from 'uuid';
import {
  LangGraphStreamRequestDto,
  LangGraphStreamRequestSchema,
  CreateThreadRequestDto,
  CreateThreadRequestSchema,
} from './dto/agent.dto';
import { ZodValidationPipe } from '../common/validation.pipe';

/**
 * Assistant information interface
 */
interface AssistantInfo {
  assistant_id: string;
  graph_id: string;
  created_at: string;
  updated_at: string;
  config: Record<string, any>;
  metadata: Record<string, any>;
}

/**
 * Thread information interface
 */
interface ThreadInfo {
  thread_id: string;
  created_at: string;
  updated_at: string;
  metadata: Record<string, any>;
}

/**
 * Run request body interface
 */
interface RunRequestBody {
  assistant_id: string;
  input?: {
    messages?: BaseMessage[];
    [key: string]: any;
  };
  config?: {
    configurable?: Record<string, any>;
    [key: string]: any;
  };
  stream_mode?: string[];
}

/**
 * LangGraph-compatible controller that provides streaming API endpoints.
 * This controller mimics the LangGraph server API that the frontend expects.
 */
@Controller()
export class LangGraphController {
  /**
   * In-memory store mapping thread IDs to their message history.
   * NOTE: This is a simple runtime store used only for the demo / quick-start.
   * In production you should persist this in a DB or external cache.
   */
  private readonly threadMessages: Map<string, any[]> = new Map();
  constructor(private readonly graphService: GraphService) {}

  /**
   * Utility: Convert LangChain BaseMessage objects to plain JSON shape expected by the frontend
   */
  private toPlainMessage(msg: BaseMessage): { type: string; content: string; id: string } {
    const type = msg._getType ? msg._getType() : (msg as any).type || 'ai';
    return {
      type,
      content: (msg as any).content ?? '',
      id: uuidv4(),
    };
  }

  /**
   * Transform event data to match the format expected by the frontend
   */
  private transformEventDataForFrontend(eventData: any, checkpoint: any): any {
    if (!eventData || typeof eventData !== 'object') {
      return { checkpoint };
    }

    // Handle different node types and ensure proper structure
    const transformedData: any = { ...eventData, checkpoint };

    // Ensure arrays are properly formatted
    if (transformedData.query_list && Array.isArray(transformedData.query_list)) {
      // Keep query_list as is for generate_query events
    }

    if (transformedData.sources_gathered && Array.isArray(transformedData.sources_gathered)) {
      // Keep sources_gathered as is for web_research events
    }

    if (transformedData.search_query && Array.isArray(transformedData.search_query)) {
      // Keep search_query as is
    }

    if (transformedData.web_research_result && Array.isArray(transformedData.web_research_result)) {
      // Keep web_research_result as is
    }

    return transformedData;
  }



  /**
   * Get assistant information - required by LangGraph SDK
   */
  @Get('assistants/:assistantId')
  async getAssistant(@Param('assistantId') assistantId: string): Promise<AssistantInfo> {
    if (assistantId !== 'agent') {
      throw new HttpException('Assistant not found', HttpStatus.NOT_FOUND);
    }

    return {
      assistant_id: 'agent',
      graph_id: 'pro-search-agent',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      config: {},
      metadata: {},
    };
  }

  /**
   * Get assistant graph - required by LangGraph SDK
   */
  @Get('assistants/:assistantId/graph')
  async getAssistantGraph(@Param('assistantId') assistantId: string) {
    if (assistantId !== 'agent') {
      throw new HttpException('Assistant not found', HttpStatus.NOT_FOUND);
    }

    // Return a simplified graph representation
    return {
      nodes: [
        { id: 'generate_query', type: 'node' },
        { id: 'web_research', type: 'node' },
        { id: 'reflection', type: 'node' },
        { id: 'finalize_answer', type: 'node' },
      ],
      edges: [
        { source: '__start__', target: 'generate_query' },
        { source: 'generate_query', target: 'web_research' },
        { source: 'web_research', target: 'reflection' },
        { source: 'reflection', target: 'finalize_answer' },
        { source: 'finalize_answer', target: '__end__' },
      ],
    };
  }

  /**
   * Create a new thread - required by LangGraph SDK
   */
  @Post('threads')
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ZodValidationPipe(CreateThreadRequestSchema))
  async createThread(@Body() body: CreateThreadRequestDto): Promise<any> {
    const threadId = uuidv4();

    // Initialize empty history for this thread
    this.threadMessages.set(threadId, []);

    return {
      thread_id: threadId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      metadata: body.metadata || {},
      status: 'idle',
      config: {},
      values: null,
    };
  }

  /**
   * Get thread information - required by LangGraph SDK
   */
  @Get('threads/:threadId')
  @HttpCode(HttpStatus.OK)
  async getThread(@Param('threadId') threadId: string) {
    return {
      thread_id: threadId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      metadata: {},
      status: 'idle',
      config: {},
      values: null,
    };
  }



  /**
   * Stream runs - this is the main endpoint for streaming agent execution
   */
  @Post('threads/:threadId/runs/stream')
  async streamRun(
    @Param('threadId') threadId: string,
    @Body(new ZodValidationPipe(LangGraphStreamRequestSchema)) body: LangGraphStreamRequestDto,
    @Res({ passthrough: false }) reply: FastifyReply,
    @Req() request: FastifyRequest,
  ) {
    try {
      // Validate assistant ID
      if (body.assistant_id !== 'agent') {
        throw new HttpException('Assistant not found', HttpStatus.NOT_FOUND);
      }

      // Set up Server-Sent Events headers
      reply.raw.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': '*',
        'Access-Control-Allow-Methods': '*',
      });

      // Helper function to send SSE events
      const sendEvent = (event: string, data: any) => {
        const eventData = `event: ${event}\ndata: ${JSON.stringify(data)}\n\n`;
        reply.raw.write(eventData);
      };

      // Extract and convert messages from input
      const inputMessages = body.input?.messages || [];
      if (inputMessages.length === 0) {
        throw new HttpException('No messages provided', HttpStatus.BAD_REQUEST);
      }

      // Convert messages to LangChain BaseMessage format
      const messages: BaseMessage[] = inputMessages.map((msg: any) => {
        switch (msg.type) {
          case 'human':
            return new HumanMessage(msg.content);
          case 'ai':
            return new AIMessage(msg.content);
          case 'system':
            return new SystemMessage(msg.content);
          default:
            return new HumanMessage(msg.content);
        }
      });

      // Create initial state
      const initialState: OverallState = {
        messages,
        query_list: [],
        search_query: [],
        web_research_result: [],
        sources_gathered: [],
        initial_search_query_count: body.config?.configurable?.initial_search_query_count || 3,
        max_research_loops: body.config?.configurable?.max_research_loops || 2,
        research_loop_count: 0,
        reasoning_model: body.config?.configurable?.reasoning_model || 'gemini-2.5-pro-preview-05-06',
      };

      // Send initial metadata event
      sendEvent('metadata', {
        run_id: uuidv4(),
        thread_id: threadId,
        assistant_id: body.assistant_id,
      });

      // Get the graph and stream execution
      const graph = this.graphService.getGraph();

      // Determine stream mode - handle multiple modes like Python backend
      const streamModes = body.stream_mode || ['updates'];
      const shouldStreamValues = streamModes.includes('values');
      const shouldStreamUpdates = streamModes.includes('updates');
      const shouldStreamMessagesTuple = streamModes.includes('messages-tuple');

      // Stream the graph execution with proper mode handling
      const stream = await graph.stream(initialState, {
        configurable: body.config?.configurable || {},
        streamMode: streamModes,
      });

      // Track current state for values streaming
      let currentState = { ...initialState };

      // Send initial values event to match Python backend behavior
      if (shouldStreamValues) {
        sendEvent('updates', {
          0: 'values',
          1: {
            ...currentState,
            checkpoint: { branch_sequence: [] },
          },
        });
      }

      for await (const chunk of stream) {
        console.log('Stream chunk received:', JSON.stringify(chunk, null, 2));

        // LangGraph stream returns [nodeName, nodeOutput] tuples
        if (Array.isArray(chunk) && chunk.length >= 2) {
          const [nodeName, nodeOutput] = chunk;

          // Update current state with node output
          if (typeof nodeOutput === 'object' && nodeOutput !== null) {
            currentState = { ...currentState, ...nodeOutput };
          }

          // Create proper checkpoint structure
          const checkpoint = { branch_sequence: [] };

          // Send node-specific updates event that matches Python backend format exactly
          if (shouldStreamUpdates) {
            let nodeEventData: any = null;

            // Create events based on node name to match Python backend format exactly
            switch (nodeName) {
              case 'generate_query':
                if (nodeOutput.query_list) {
                  nodeEventData = {
                    generate_query: {
                      query_list: nodeOutput.query_list,
                    },
                    checkpoint,
                  };
                }
                break;

              case 'web_research':
                nodeEventData = {
                  web_research: {
                    sources_gathered: nodeOutput.sources_gathered || [],
                    search_query: nodeOutput.search_query || [],
                    web_research_result: nodeOutput.web_research_result || [],
                  },
                  checkpoint,
                };
                break;

              case 'reflection':
                // Handle reflection result from the _reflection_result field
                const reflectionData = nodeOutput._reflection_result || nodeOutput;
                nodeEventData = {
                  reflection: {
                    is_sufficient: reflectionData.is_sufficient,
                    follow_up_queries: reflectionData.follow_up_queries || [],
                    knowledge_gap: reflectionData.knowledge_gap,
                    research_loop_count: reflectionData.research_loop_count,
                  },
                  checkpoint,
                };
                break;

              case 'finalize_answer':
                nodeEventData = {
                  finalize_answer: {
                    messages: nodeOutput.messages || [],
                    sources_gathered: nodeOutput.sources_gathered || [],
                  },
                  checkpoint,
                };
                break;

              default:
                // For any other node types, return a generic structure
                nodeEventData = {
                  [nodeName]: nodeOutput,
                  checkpoint,
                };
                break;
            }

            if (nodeEventData) {
              sendEvent('updates', {
                0: 'updates',
                1: nodeEventData,
              });
            }
          }

          // Send values event with updated state
          if (shouldStreamValues) {
            sendEvent('updates', {
              0: 'values',
              1: {
                ...currentState,
                checkpoint,
              },
            });
          }

          // Send messages-tuple event if requested
          if (shouldStreamMessagesTuple && currentState.messages) {
            sendEvent('updates', {
              0: 'messages-tuple',
              1: {
                messages: currentState.messages,
                checkpoint,
              },
            });
          }
        } else if (typeof chunk === 'object' && chunk !== null) {
          // Handle object-based chunks (fallback)
          const checkpoint = { branch_sequence: [] };
          currentState = { ...currentState, ...chunk };

          sendEvent('updates', {
            0: 'updates',
            1: {
              ...chunk,
              checkpoint,
            },
          });
        }

        // Add small delay to prevent overwhelming the client
        await new Promise((resolve) => setTimeout(resolve, 10));
      }

      // Send end event
      sendEvent('end', {});

      // Compute final state (non-stream) to persist history
      const finalState = await graph.invoke(initialState, {
        configurable: body.config?.configurable || {},
      });

      // Combine input and output messages
      const storedMessages = [
        ...inputMessages,
        ...(Array.isArray(finalState.messages)
          ? finalState.messages.map((m: BaseMessage) => this.toPlainMessage(m))
          : []),
      ];
      this.threadMessages.set(threadId, storedMessages);

      // Close the connection
      reply.raw.end();

    } catch (error) {
      console.error('Error in stream run:', error);

      // Send error event if connection is still open
      if (!reply.raw.destroyed) {
        const errorEvent = `event: error\ndata: ${JSON.stringify({
          error: error instanceof Error ? error.message : 'Unknown error',
        })}\n\n`;
        reply.raw.write(errorEvent);
        reply.raw.end();
      }
    }
  }

  /**
   * Non-streaming run endpoint for compatibility
   */
  @Post('threads/:threadId/runs')
  async createRun(
    @Param('threadId') threadId: string,
    @Body() body: {
      assistant_id: string;
      input?: any;
      config?: any;
    },
  ) {
    try {
      // Validate assistant ID
      if (body.assistant_id !== 'agent') {
        throw new HttpException('Assistant not found', HttpStatus.NOT_FOUND);
      }

      // Extract and convert messages from input
      const inputMessages = body.input?.messages || [];
      if (inputMessages.length === 0) {
        throw new HttpException('No messages provided', HttpStatus.BAD_REQUEST);
      }

      // Convert messages to LangChain BaseMessage format
      const messages: BaseMessage[] = inputMessages.map((msg: any) => {
        switch (msg.type) {
          case 'human':
            return new HumanMessage(msg.content);
          case 'ai':
            return new AIMessage(msg.content);
          case 'system':
            return new SystemMessage(msg.content);
          default:
            return new HumanMessage(msg.content);
        }
      });

      // Create initial state
      const initialState: OverallState = {
        messages,
        query_list: [],
        search_query: [],
        web_research_result: [],
        sources_gathered: [],
        initial_search_query_count: body.config?.configurable?.initial_search_query_count || 3,
        max_research_loops: body.config?.configurable?.max_research_loops || 2,
        research_loop_count: 0,
        reasoning_model: body.config?.configurable?.reasoning_model || 'gemini-2.5-pro-preview-05-06',
      };

      // Get the graph and run it
      const graph = this.graphService.getGraph();
      const result = await graph.invoke(initialState, {
        configurable: body.config?.configurable || {},
      });

      // Persist history
      const storedMessages = [
        ...inputMessages,
        ...(Array.isArray(result.messages)
          ? result.messages.map((m: BaseMessage) => this.toPlainMessage(m))
          : []),
      ];
      this.threadMessages.set(threadId, storedMessages);

      return {
        run_id: uuidv4(),
        thread_id: threadId,
        assistant_id: body.assistant_id,
        status: 'success',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        output: result,
      };

    } catch (error) {
      console.error('Error in create run:', error);
      throw new HttpException(
        error instanceof Error ? error.message : 'Unknown error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get run information - required by LangGraph SDK
   */
  @Get('threads/:threadId/runs/:runId')
  async getRun(
    @Param('threadId') threadId: string,
    @Param('runId') runId: string,
  ) {
    return {
      run_id: runId,
      thread_id: threadId,
      assistant_id: 'agent',
      status: 'success',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
  }

  /**
   * Get thread history - required by LangGraph SDK
   */
  @Post('threads/:threadId/history')
  @HttpCode(HttpStatus.OK)
  async getThreadHistory(
    @Param('threadId') threadId: string,
    @Body() body: any,
  ) {
    // Return any stored messages for this thread; default to empty array
    return this.threadMessages.get(threadId) || [];
  }

  /**
   * Get thread state - required by LangGraph SDK
   */
  @Get('threads/:threadId/state')
  async getThreadState(@Param('threadId') threadId: string) {
    return {
      values: {
        messages: [],
        query_list: [],
        search_query: [],
        web_research_result: [],
        sources_gathered: [],
        initial_search_query_count: 3,
        max_research_loops: 2,
        research_loop_count: 0,
        reasoning_model: 'gemini-2.5-pro-preview-05-06',
      },
      next: [],
      config: {
        configurable: {},
      },
      metadata: {},
      created_at: new Date().toISOString(),
      parent_config: null,
    };
  }

  /**
   * Update thread state - required by LangGraph SDK
   */
  @Post('threads/:threadId/state')
  async updateThreadState(
    @Param('threadId') threadId: string,
    @Body() body: any,
  ) {
    // In a real implementation, this would update the thread state in a database
    // For now, we'll just return a success response
    return {
      success: true,
      updated_at: new Date().toISOString(),
    };
  }
}
