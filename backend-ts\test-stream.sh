#!/bin/bash

echo "Testing TypeScript backend streaming endpoint..."

curl -X POST http://localhost:2024/threads/test-thread/runs/stream \
  -H "Content-Type: application/json" \
  -d '{
    "assistant_id": "agent",
    "input": {
      "messages": [
        {
          "type": "human",
          "content": "What is the capital of France?"
        }
      ]
    },
    "stream_mode": ["values", "updates"],
    "config": {
      "configurable": {
        "initial_search_query_count": 2,
        "max_research_loops": 1,
        "reasoning_model": "gemini-2.5-pro-preview-05-06"
      }
    }
  }' \
  --no-buffer -v
