// Use built-in fetch (Node.js 18+) or fallback to https
let fetch;
try {
  fetch = globalThis.fetch;
} catch (e) {
  const https = require('https');
  const http = require('http');
}

async function testStream() {
  const url = 'http://localhost:2024/threads/test-thread/runs/stream';
  const payload = {
    assistant_id: 'agent',
    input: {
      messages: [
        {
          type: 'human',
          content: 'What is the capital of France?'
        }
      ]
    },
    stream_mode: ['values', 'updates'],
    config: {
      configurable: {
        initial_search_query_count: 2,
        max_research_loops: 1,
        reasoning_model: 'gemini-2.5-pro-preview-05-06'
      }
    }
  };

  try {
    console.log('Testing streaming endpoint...');
    console.log('Request payload:', JSON.stringify(payload, null, 2));
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      console.error('HTTP Error:', response.status, response.statusText);
      const errorText = await response.text();
      console.error('Error response:', errorText);
      return;
    }

    console.log('Response headers:', response.headers.raw());
    console.log('\n--- Streaming Events ---');

    const reader = response.body;
    let buffer = '';

    reader.on('data', (chunk) => {
      buffer += chunk.toString();
      
      // Process complete events
      const lines = buffer.split('\n');
      buffer = lines.pop(); // Keep incomplete line in buffer
      
      for (const line of lines) {
        if (line.startsWith('event: ')) {
          const eventType = line.substring(7);
          console.log(`\nEvent Type: ${eventType}`);
        } else if (line.startsWith('data: ')) {
          const eventData = line.substring(6);
          try {
            const parsed = JSON.parse(eventData);
            console.log('Event Data:', JSON.stringify(parsed, null, 2));
          } catch (e) {
            console.log('Raw Event Data:', eventData);
          }
        }
      }
    });

    reader.on('end', () => {
      console.log('\n--- Stream Ended ---');
    });

    reader.on('error', (error) => {
      console.error('Stream Error:', error);
    });

  } catch (error) {
    console.error('Test Error:', error);
  }
}

testStream();
